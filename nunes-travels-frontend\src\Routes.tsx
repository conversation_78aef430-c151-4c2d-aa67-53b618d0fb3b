import React from 'react';

import { Route, BrowserRouter as Router, Routes } from 'react-router-dom';

import BillRegister from '@pages/Bill/BillRegister';
import BillRegisterDetail from '@pages/Bill/BillRegisterDetail';
import BillRegisterEdit from '@pages/Bill/BillRegisterEdit';
import DirectBill from '@pages/Bill/DirectBill';
import DirectBillDetail from '@pages/Bill/DirectBillDetail';
import DirectBillEdit from '@pages/Bill/DirectBillEdit';
import DirectBillView from '@pages/Bill/DirectBillView';
// import NewBill from '@pages/Bill/NewBill';
// import NewBillEdit from '@pages/Bill/NewBillEdit';
// import NewBillEditHire from '@pages/Bill/NewBillEditHire';
import PendingBillEdit from '@pages/Bill/PendingBillEdit';
import PendingBills from '@pages/Bill/PendingBills';
// import PrintBill from '@pages/Bill/PrintBill';
// import PrintBillRegister from '@pages/Bill/PrintBillRegister';
import Contact from '@pages/Contact';
import ContractDetails from '@pages/Contracts/ContractDetails';
import ContractView from '@pages/Contracts/ContractView';
import Contracts from '@pages/Contracts/index';
import ControlPanel from '@pages/ControlPanel';
import BillDates from '@pages/ControlPanel/BillDates';
import Companies from '@pages/ControlPanel/Companies';
import CompanyTariff from '@pages/ControlPanel/Companies/CompanyTariff';
// import Companies from '@pages/ControlPanel/Companies';
import Driver from '@pages/ControlPanel/Drivers';
import ExtraCharges from '@pages/ControlPanel/ExtraCharges';
import GeneralTariff from '@pages/ControlPanel/GeneralTariff';
import GstData from '@pages/ControlPanel/GST';
import Hotel from '@pages/ControlPanel/Hotel';
import NewCompanies from '@pages/ControlPanel/NewCompanies';
import OwnerCodes from '@pages/ControlPanel/OwnerCode';
import Sector from '@pages/ControlPanel/Sector';
import StaffList from '@pages/ControlPanel/Staff';
import SubAgent from '@pages/ControlPanel/SubAgent';
import SubAgentTariff from '@pages/ControlPanel/SubAgent/SubAgentTariff';
import Transfer from '@pages/ControlPanel/Transfer';
import Users from '@pages/ControlPanel/Users';
import VehicleNumber from '@pages/ControlPanel/VehicleNumber';
import VehicleType from '@pages/ControlPanel/VehicleType';
import HireChart from '@pages/HireChart';
import EditHireChart from '@pages/HireChart/EditHireChart';
import PdcHire from '@pages/HireChart/PdcHire';
import Home from '@pages/Home';
import LoginPage from '@pages/Login';
import Profile from '@pages/Profile';
import Proforma from '@pages/Proforma';
import EditProforma from '@pages/Proforma/EditProforma';
import ProformaDetails from '@pages/Proforma/ProformaDetails';
// import DirectBill from '@components/Bill/DirectBill';
import BreakdownIncidence from '@pages/Reports/BreakdownIncidence';
import DriverAttendance from '@pages/Reports/DriverAttendance';
import SingleDriverAttendance from '@pages/Reports/SingleDriverAttendance';
import UnbilledHires from '@pages/Reports/UnbilledHires';
import VehicleDrivers from '@pages/Reports/VehicleDrivers';
// import VehicleNumbers from '@pages/Reports/VehicleNumbers';
import VehicleNumbersReport from '@pages/Reports/VehicleNumbers';
import VehicleSuppliers from '@pages/Reports/VehicleSuppliers';

import MiniDrawer from '@components/layouts/layout';
import PrivateRoute from '@components/PrivateRoute/PrivateRoute';
import RoleBasedRoute, { HiresRoute, BillsRoute, ControlPanelRoute } from '@components/RoleBasedRoute/RoleBasedRoute';

// import NotFound from './pages/NotFound';

const AppRoutes: React.FC = () => (
  <Router>
    <Routes>
      <Route path="/login" element={<LoginPage />} />
      <Route
        path="/"
        element={
          <PrivateRoute>
            <MiniDrawer />
          </PrivateRoute>
        }
      >
        <Route index element={<Home />} />
        <Route path="contacts" element={<HiresRoute><Contact /></HiresRoute>} />
        <Route path="hirechart" element={<HiresRoute><HireChart /></HiresRoute>} />
        <Route path="/editHire" element={<HiresRoute><EditHireChart /></HiresRoute>} />
        <Route path="/pdcHire" element={<HiresRoute><PdcHire /></HiresRoute>} />
        <Route path="proforma" element={<HiresRoute><Proforma /></HiresRoute>} />
        <Route path="/newBill/:id/edit" element={<BillsRoute><DirectBillEdit /></BillsRoute>} />
        <Route path="/newBill/:id" element={<BillsRoute><DirectBillDetail /></BillsRoute>} />
        <Route path="/bill/view" element={<BillsRoute><DirectBillView /></BillsRoute>} />
        {/* <Route path="/newBill" element={<NewBill />} />
        <Route path="/newBill/edit" element={<NewBillEdit />} />
        <Route path="/newBill/edit/:id/hire" element={<NewBillEditHire />} /> */}
        <Route path="/billRegister/:id/edit" element={<BillsRoute><BillRegisterEdit /></BillsRoute>} />
        <Route path="/billRegister/:id" element={<BillsRoute><BillRegisterDetail /></BillsRoute>} />
        <Route path="/proformaDetails/:id" element={<HiresRoute><ProformaDetails /></HiresRoute>} />
        <Route path="/edit-proforma/:id" element={<HiresRoute><EditProforma /></HiresRoute>} />
        <Route path="contracts" element={<HiresRoute><Contracts /></HiresRoute>} />
        <Route path="/contractDetails/:id" element={<HiresRoute><ContractDetails /></HiresRoute>} />
        <Route path="/contracts/view" element={<HiresRoute><ContractView /></HiresRoute>} />
        <Route path="/profile" element={<Profile />} />
        <Route path="/pendingBills" element={<BillsRoute><PendingBills /></BillsRoute>} />
        <Route path="/pendingBills/:id" element={<BillsRoute><PendingBillEdit /></BillsRoute>} />
        <Route path="/newBill" element={<BillsRoute><DirectBill /></BillsRoute>} />
        <Route path="/billRegister" element={<BillsRoute><BillRegister /></BillsRoute>} />
        <Route path="/profile" element={<Profile />} />
        <Route path="/unbilledHires" element={<HiresRoute><UnbilledHires /></HiresRoute>} />
        <Route path="/vehicleSuppliers" element={<HiresRoute><VehicleSuppliers /></HiresRoute>} />
        {/* <Route path="/vehicleNumbers" element={<VehicleNumbers />} /> */}
        <Route
          path="/VehicleNumbersReport"
          element={<HiresRoute><VehicleNumbersReport /></HiresRoute>}
        />
        <Route path="/VehicleDriversReport" element={<HiresRoute><VehicleDrivers /></HiresRoute>} />
        <Route path="/BreakdownIncidence" element={<HiresRoute><BreakdownIncidence /></HiresRoute>} />
        <Route
          path="/all-drivers-overview-report"
          element={<HiresRoute><DriverAttendance /></HiresRoute>}
        />
        <Route
          path="/reports/driver-attendance/:driverId"
          element={<HiresRoute><SingleDriverAttendance /></HiresRoute>}
        />
        <Route path="/controlpanel" element={<ControlPanelRoute><ControlPanel /></ControlPanelRoute>} />
        <Route path="/vehicleType" element={<ControlPanelRoute><VehicleType /></ControlPanelRoute>} />
        <Route path="/vehicleNumber" element={<ControlPanelRoute><VehicleNumber /></ControlPanelRoute>} />
        <Route path="/ownerCode" element={<ControlPanelRoute><OwnerCodes /></ControlPanelRoute>} />
        <Route path="/driver" element={<HiresRoute><Driver /></HiresRoute>} />
        <Route path="/staff" element={<ControlPanelRoute><StaffList /></ControlPanelRoute>} />
        <Route path="/transfer" element={<ControlPanelRoute><Transfer /></ControlPanelRoute>} />
        <Route path="/subAgent" element={<ControlPanelRoute><SubAgent /></ControlPanelRoute>} />
        <Route path="/sector" element={<ControlPanelRoute><Sector /></ControlPanelRoute>} />
        <Route path="/companyTariff" element={<HiresRoute><CompanyTariff /></HiresRoute>} />
        <Route path="/companyTariff/:id" element={<HiresRoute><CompanyTariff /></HiresRoute>} />
        <Route path="/users" element={<ControlPanelRoute><Users /></ControlPanelRoute>} />
        <Route path="/hotel" element={<ControlPanelRoute><Hotel /></ControlPanelRoute>} />
        <Route path="/billDates" element={<ControlPanelRoute><BillDates /></ControlPanelRoute>} />
        <Route path="/newCompanies" element={<ControlPanelRoute><NewCompanies /></ControlPanelRoute>} />
        <Route path="/Companies" element={<ControlPanelRoute><Companies /></ControlPanelRoute>} />
        <Route path="/extra" element={<ControlPanelRoute><ExtraCharges /></ControlPanelRoute>} />
        <Route path="/gst" element={<ControlPanelRoute><GstData /></ControlPanelRoute>} />
        <Route path="/generalTariff" element={<ControlPanelRoute><GeneralTariff /></ControlPanelRoute>} />
        <Route path="/subAgentTariff/:id" element={<ControlPanelRoute><SubAgentTariff /></ControlPanelRoute>} />
      </Route>

      <Route path="/" element={<Home />} />
    </Routes>
  </Router>
);

export default AppRoutes;
