import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define user roles and permissions
export const USER_ROLES = {
  ADMIN: 2,
  EDITOR: 3,
} as const;

export const PERMISSIONS = {
  ADMIN: ['admin', 'hires', 'bills', 'control_panel'],
  EDITOR: ['hires'],
} as const;

export interface User {
  userid: number;
  useremail: string;
  userlevel: number;
  firstname?: string;
  lastname?: string;
  year?: string;
}

export interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  userPermissions: string[];
  login: (userData: User) => void;
  logout: () => void;
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  isAdmin: () => boolean;
  isEditor: () => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);

  // Get user permissions based on user level
  const getUserPermissions = (userlevel: number): string[] => {
    switch (userlevel) {
      case USER_ROLES.ADMIN:
        return [...PERMISSIONS.ADMIN];
      case USER_ROLES.EDITOR:
        return [...PERMISSIONS.EDITOR];
      default:
        return [];
    }
  };

  const userPermissions = user ? getUserPermissions(user.userlevel) : [];

  // Initialize auth state from sessionStorage on mount
  useEffect(() => {
    const storedAuth = sessionStorage.getItem('isAuthenticated');
    const storedUser = sessionStorage.getItem('user');

    if (storedAuth === 'true' && storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        // Validate that the user data has the required fields
        if (userData && typeof userData.userlevel === 'number') {
          setUser(userData);
          setIsAuthenticated(true);
        } else {
          console.warn('Invalid user data format, clearing session');
          logout();
        }
      } catch (error) {
        console.error('Error parsing stored user data:', error);
        logout();
      }
    }
  }, []);

  const login = (userData: User) => {
    setUser(userData);
    setIsAuthenticated(true);
    sessionStorage.setItem('isAuthenticated', 'true');
    sessionStorage.setItem('user', JSON.stringify(userData));
  };

  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
    sessionStorage.removeItem('isAuthenticated');
    sessionStorage.removeItem('user');
  };

  const hasPermission = (permission: string): boolean => {
    return userPermissions.includes(permission);
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => userPermissions.includes(permission));
  };

  const isAdmin = (): boolean => {
    return user?.userlevel === USER_ROLES.ADMIN;
  };

  const isEditor = (): boolean => {
    return user?.userlevel === USER_ROLES.EDITOR;
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    userPermissions,
    login,
    logout,
    hasPermission,
    hasAnyPermission,
    isAdmin,
    isEditor,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
