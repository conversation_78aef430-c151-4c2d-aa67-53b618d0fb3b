import { ReactNode } from 'react';

import AssessmentOutlinedIcon from '@mui/icons-material/AssessmentOutlined';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import DashboardOutlinedIcon from '@mui/icons-material/DashboardOutlined';
import DescriptionOutlinedIcon from '@mui/icons-material/DescriptionOutlined';
import GroupOutlinedIcon from '@mui/icons-material/GroupOutlined';
import HistoryEduOutlinedIcon from '@mui/icons-material/HistoryEduOutlined';
import ReceiptLongIcon from '@mui/icons-material/ReceiptLong';
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined';

export interface IMenuItem {
  title: string;
  link?: string;
  onClick?: () => void;
  icon?: ReactNode;
  items?: IMenuItem[];
  requiredPermissions?: string[];
}

export const menu: IMenuItem[] = [
  {
    title: 'Home',
    link: '/',
    icon: <DashboardOutlinedIcon />,
  },
  {
    title: 'Contacts',
    link: '/contacts',
    icon: <GroupOutlinedIcon />,
    requiredPermissions: ['hires'],
  },
  {
    title: 'Hire Chart',
    icon: <CalendarMonthIcon />,
    requiredPermissions: ['hires'],
    items: [
      {
        title: 'New Hire',
        link: '/hirechart',
        requiredPermissions: ['hires'],
      },
      {
        title: 'Edit Hire',
        link: '/editHire',
        requiredPermissions: ['hires'],
      },
      {
        title: 'PDC Hire',
        link: '/pdcHire',
        requiredPermissions: ['hires'],
      },
    ],
  },
  {
    title: 'Bill',
    link: '/newBill',
    icon: <ReceiptLongIcon />,
    requiredPermissions: ['bills'],
    items: [
      {
        title: 'Pending Bills',
        link: '/pendingBills',
        requiredPermissions: ['bills'],
      },
      {
        title: 'Bill Register',
        link: '/billRegister',
        requiredPermissions: ['bills'],
      },
      {
        title: 'New Bill',
        link: '/newBill',
        requiredPermissions: ['bills'],
      },
      // {
      //   title: 'Direct Bill',
      //   link: '/directBill',
      // },
    ],
  },
  {
    title: 'Proforma',
    link: '/proforma',
    icon: <DescriptionOutlinedIcon />,
    requiredPermissions: ['hires'],
  },
  {
    title: 'Contracts',
    link: '/contracts',
    icon: <HistoryEduOutlinedIcon />,
    requiredPermissions: ['hires'],
  },
  {
    title: 'Reports',
    link: '/unbilledHires',
    icon: <AssessmentOutlinedIcon />,
    requiredPermissions: ['hires'],
    items: [
      {
        title: 'Unbilled Hires',
        link: '/unbilledHires',
        requiredPermissions: ['hires'],
      },
      {
        title: 'Vehicle Suppliers',
        link: '/vehicleSuppliers',
        requiredPermissions: ['hires'],
      },
      {
        title: 'Vehicle Numbers',
        link: '/VehicleNumbersReport',
        requiredPermissions: ['hires'],
      },
      {
        title: 'Vehicle Drivers',
        link: '/VehicleDriversReport',
        requiredPermissions: ['hires'],
      },
      {
        title: 'Breakdown Incidence',
        link: '/breakdownIncidence',
        requiredPermissions: ['hires'],
      },
      {
        title: 'Driver Attendance Overview',
        link: '/all-drivers-overview-report',
        requiredPermissions: ['hires'],
      },
      {
        title: 'Mark Driver Absent',
        link: '/driver',
        requiredPermissions: ['hires'],
      },
    ],
  },
  {
    title: 'Control Panel',
    link: 'controlpanel',
    icon: <SettingsOutlinedIcon />,
    requiredPermissions: ['control_panel'],
  },
];
