-- SQL script to add Editor user with userlevel = 3
-- This script can be run manually on the current database for immediate testing

-- Check if editor user already exists
SELECT COUNT(*) as editor_exists FROM logon WHERE useremail = 'editor';

-- Insert Editor user if it doesn't exist
-- Note: Replace 'editor123' with a secure password
INSERT INTO logon (useremail, password, firstname, lastname, userlevel) 
SELECT 'editor', MD5('editor123'), 'Editor', 'User', 3
WHERE NOT EXISTS (SELECT 1 FROM logon WHERE useremail = 'editor');

-- Verify the insertion
SELECT userid, useremail, firstname, lastname, userlevel FROM logon WHERE useremail = 'editor';

-- Show all users for verification
SELECT userid, useremail, firstname, lastname, userlevel FROM logon ORDER BY userlevel, userid;
