import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { Box, Typography, Paper } from '@mui/material';
import LockIcon from '@mui/icons-material/Lock';

interface RoleBasedRouteProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  requiredRole?: number;
  fallbackPath?: string;
  showAccessDenied?: boolean;
}

const AccessDeniedPage: React.FC = () => (
  <Box
    display="flex"
    flexDirection="column"
    alignItems="center"
    justifyContent="center"
    minHeight="60vh"
    p={4}
  >
    <Paper
      elevation={3}
      sx={{
        p: 4,
        textAlign: 'center',
        maxWidth: 400,
        width: '100%',
      }}
    >
      <LockIcon
        sx={{
          fontSize: 64,
          color: 'error.main',
          mb: 2,
        }}
      />
      <Typography variant="h5" gutterBottom color="error">
        Access Denied
      </Typography>
      <Typography variant="body1" color="text.secondary">
        You don't have permission to access this page. Please contact your administrator if you believe this is an error.
      </Typography>
    </Paper>
  </Box>
);

const RoleBasedRoute: React.FC<RoleBasedRouteProps> = ({
  children,
  requiredPermissions = [],
  requiredRole,
  fallbackPath = '/',
  showAccessDenied = true,
}) => {
  const { isAuthenticated, user, hasAnyPermission, hasPermission } = useAuth();

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // If no user data, redirect to login
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // Check role-based access
  if (requiredRole && user.userlevel !== requiredRole) {
    if (showAccessDenied) {
      return <AccessDeniedPage />;
    }
    return <Navigate to={fallbackPath} replace />;
  }

  // Check permission-based access
  if (requiredPermissions.length > 0) {
    const hasAccess = hasAnyPermission(requiredPermissions);
    
    if (!hasAccess) {
      if (showAccessDenied) {
        return <AccessDeniedPage />;
      }
      return <Navigate to={fallbackPath} replace />;
    }
  }

  // User has access, render the children
  return <>{children}</>;
};

export default RoleBasedRoute;

// Convenience components for common use cases
export const AdminOnlyRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <RoleBasedRoute requiredPermissions={['admin']}>
    {children}
  </RoleBasedRoute>
);

export const HiresRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <RoleBasedRoute requiredPermissions={['hires']}>
    {children}
  </RoleBasedRoute>
);

export const BillsRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <RoleBasedRoute requiredPermissions={['bills']}>
    {children}
  </RoleBasedRoute>
);

export const ControlPanelRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <RoleBasedRoute requiredPermissions={['control_panel']}>
    {children}
  </RoleBasedRoute>
);
