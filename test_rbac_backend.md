# Backend RBAC Testing

## Test the backend RBAC system using curl or Postman

### 1. <PERSON><PERSON> as Editor
```bash
curl -X POST http://localhost:8000/api/login \
  -H "Content-Type: application/json" \
  -d '{"username": "<EMAIL>", "password": "editor123"}' \
  -c cookies.txt
```

### 2. Test Allowed Endpoint (Hires)
```bash
curl -X GET http://localhost:8000/api/hirechart \
  -b cookies.txt
```
**Expected**: Should return data (200 OK)

### 3. Test Blocked Endpoint (Bills)
```bash
curl -X GET http://localhost:8000/api/bill-reg \
  -b cookies.txt
```
**Expected**: Should return 403 Forbidden with error message

### 4. Test Blocked Endpoint (Control Panel)
```bash
curl -X GET http://localhost:8000/api/users \
  -b cookies.txt
```
**Expected**: Should return 403 Forbidden with error message

### 5. <PERSON>gin as Admin
```bash
curl -X POST http://localhost:8000/api/login \
  -H "Content-Type: application/json" \
  -d '{"username": "jpnunes_admin", "password": "admin_password"}' \
  -c admin_cookies.txt
```

### 6. Test Admin Access to All Endpoints
```bash
# Should work for admin
curl -X GET http://localhost:8000/api/bill-reg -b admin_cookies.txt
curl -X GET http://localhost:8000/api/users -b admin_cookies.txt
curl -X GET http://localhost:8000/api/hirechart -b admin_cookies.txt
```
**Expected**: All should return 200 OK

## Troubleshooting

If the RBAC is not working:

1. **Check if the role filter is being applied**:
   - Look at the Routes.php file
   - Ensure routes have `'filter' => ['AuthCheck', 'role:permission']`

2. **Check session data**:
   - Verify that `userlevel` is being stored in the session
   - Check that the login response includes `userlevel`

3. **Check filter registration**:
   - Verify RoleFilter is registered in Filters.php
   - Check for any PHP errors in the logs
