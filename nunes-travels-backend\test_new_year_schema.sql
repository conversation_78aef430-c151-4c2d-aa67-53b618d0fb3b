-- Test script to verify new financial year schema creation and RBAC preservation
-- This script tests the LogonModel::createSchema functionality

-- First, let's check the current year's logon table
SELECT 'Current Year (2025) Logon Table:' as info;
SELECT userid, useremail, firstname, lastname, userlevel FROM nunes2025.logon ORDER BY userlevel, userid;

-- Test creating a new year schema (2026) - this would normally be done through the application
-- For testing purposes, we'll simulate what the createSchema method does

-- Create the new schema
CREATE SCHEMA IF NOT EXISTS nunes2026;

-- Create the logon table structure
CREATE TABLE IF NOT EXISTS nunes2026.logon LIKE nunes2025.logon;

-- Copy all logon data from 2025 to 2026
INSERT IGNORE nunes2026.logon SELECT * FROM nunes2025.logon;

-- Verify the data was copied correctly
SELECT 'New Year (2026) Logon Table:' as info;
SELECT userid, useremail, firstname, lastname, userlevel FROM nunes2026.logon ORDER BY userlevel, userid;

-- Verify that the Editor user (userlevel = 3) was preserved
SELECT 'Editor Users in New Year:' as info;
SELECT userid, useremail, firstname, lastname, userlevel FROM nunes2026.logon WHERE userlevel = 3;

-- Clean up test schema (optional - comment out if you want to keep it)
-- DROP SCHEMA IF EXISTS nunes2026;
