import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Box, Typography, Paper } from '@mui/material';

const DebugAuth: React.FC = () => {
  console.log('DebugAuth component rendering...');

  try {
    const { user, isAuthenticated, userPermissions } = useAuth();
    console.log('Auth data:', { user, isAuthenticated, userPermissions });

    return (
      <Paper sx={{ p: 2, m: 2, backgroundColor: '#f5f5f5', border: '2px solid red' }}>
        <Typography variant="h6" gutterBottom>
          🔍 Debug Auth Information
        </Typography>
        <Typography variant="body2">
          <strong>Is Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}
        </Typography>
        <Typography variant="body2">
          <strong>User:</strong> {user ? JSON.stringify(user, null, 2) : 'null'}
        </Typography>
        <Typography variant="body2">
          <strong>User Permissions:</strong> {JSON.stringify(userPermissions)}
        </Typography>
        <Typography variant="body2">
          <strong>SessionStorage isAuthenticated:</strong> {sessionStorage.getItem('isAuthenticated')}
        </Typography>
        <Typography variant="body2">
          <strong>SessionStorage user:</strong> {sessionStorage.getItem('user')}
        </Typography>
      </Paper>
    );
  } catch (error) {
    console.error('Error in DebugAuth:', error);
    return (
      <Paper sx={{ p: 2, m: 2, backgroundColor: '#ffebee', border: '2px solid red' }}>
        <Typography variant="h6" color="error">
          ❌ Error in DebugAuth Component
        </Typography>
        <Typography variant="body2">
          {String(error)}
        </Typography>
      </Paper>
    );
  }
};

export default DebugAuth;
