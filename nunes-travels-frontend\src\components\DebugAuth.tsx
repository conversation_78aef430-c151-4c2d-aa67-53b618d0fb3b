import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Box, Typography, Paper } from '@mui/material';

const DebugAuth: React.FC = () => {
  const { user, isAuthenticated, userPermissions } = useAuth();

  return (
    <Paper sx={{ p: 2, m: 2, backgroundColor: '#f5f5f5' }}>
      <Typography variant="h6" gutterBottom>
        Debug Auth Information
      </Typography>
      <Typography variant="body2">
        <strong>Is Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}
      </Typography>
      <Typography variant="body2">
        <strong>User:</strong> {user ? JSON.stringify(user, null, 2) : 'null'}
      </Typography>
      <Typography variant="body2">
        <strong>User Permissions:</strong> {JSON.stringify(userPermissions)}
      </Typography>
      <Typography variant="body2">
        <strong>SessionStorage isAuthenticated:</strong> {sessionStorage.getItem('isAuthenticated')}
      </Typography>
      <Typography variant="body2">
        <strong>SessionStorage user:</strong> {sessionStorage.getItem('user')}
      </Typography>
    </Paper>
  );
};

export default DebugAuth;
