<?php

namespace App\Filters;

use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\Filters\FilterInterface;

class RoleFilter implements FilterInterface
{
    /**
     * Role permissions mapping
     * Key: userlevel, Value: array of permissions
     */
    const ROLE_PERMISSIONS = [
        2 => ['admin', 'hires', 'bills', 'control_panel'], // Admin (userlevel = 2)
        3 => ['hires'] // Editor (userlevel = 3)
    ];

    /**
     * Route patterns mapping to permissions
     * Key: permission, Value: route pattern
     */
    const ROUTE_PATTERNS = [
        'admin' => [
            'api/users*',
            'api/set-db-year*'
        ],
        'hires' => [
            'api/hirechart*',
            'api/hirechart/*',
            'api/stats*',
            'api/drivers*',
            'api/drivers/*',
            'api/contract*',
            'api/contract/*',
            'api/cpart*',
            'api/cpart/*',
            'api/company*',
            'api/company/*',
            'api/company2*',
            'api/company2/*',
            'api/contact*',
            'api/contact/*',
            'api/rates*',
            'api/rates/*',
            'api/general-tariff*',
            'api/general-tariff/*',
            'api/tariff*',
            'api/tariff/*',
            'api/subagent*',
            'api/subagent/*',
            'api/vehicle-no*',
            'api/vehicle-no/*',
            'api/driver*',
            'api/driver/*',
            'api/vehicle-type*',
            'api/vehicle-type/*',
            'api/transfer*',
            'api/transfer/*',
            'api/owner-code*',
            'api/owner-code/*',
            'api/hotel*',
            'api/hotel/*',
            'api/sector*',
            'api/sector/*',
            'api/extra*',
            'api/extra/*',
            'api/gst*',
            'api/gst/*'
        ],
        'bills' => [
            'api/bill-reg*',
            'api/bill-reg/*',
            'api/bill*',
            'api/bill/*',
            'api/dbill-hires*',
            'api/dbill-hires/*',
            'api/ptoll*',
            'api/ptoll/*',
            'api/bill-date*',
            'api/bill-date/*',
            'api/proformareg*',
            'api/proformareg/*',
            'api/proforma*',
            'api/proforma/*'
        ],
        'control_panel' => [
            'api/users*',
            'api/users/*',
            'api/staff*',
            'api/staff/*',
            'api/general-rates*',
            'api/general-rates/*',
            'api/sub-agent*',
            'api/sub-agent/*',
            'api/additional*',
            'api/additional/*'
        ]
    ];

    public function before(RequestInterface $request, $arguments = null)
    {
        // Check if user is authenticated
        if (!session()->has('userlevel')) {
            $response = service('response');
            $response->setStatusCode(401)->setBody(json_encode(['error' => 'Authentication required']));
            return $response;
        }

        // Get user level from session
        $userLevel = session()->get('userlevel');
        
        // Get user permissions based on role
        $userPermissions = self::ROLE_PERMISSIONS[$userLevel] ?? [];
        
        // If no permissions defined for this role, deny access
        if (empty($userPermissions)) {
            $response = service('response');
            $response->setStatusCode(403)->setBody(json_encode(['error' => 'Access denied - no permissions']));
            return $response;
        }

        // Get the current route
        $currentRoute = $request->getUri()->getPath();
        
        // Check if user has permission to access this route
        $hasPermission = $this->checkRoutePermission($currentRoute, $userPermissions);
        
        if (!$hasPermission) {
            $response = service('response');
            $response->setStatusCode(403)->setBody(json_encode([
                'error' => 'Access denied - insufficient permissions',
                'route' => $currentRoute,
                'user_level' => $userLevel,
                'permissions' => $userPermissions
            ]));
            return $response;
        }

        // Allow access
        return null;
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Do nothing after request
    }

    /**
     * Check if user has permission to access the given route
     */
    private function checkRoutePermission(string $route, array $userPermissions): bool
    {
        foreach ($userPermissions as $permission) {
            if (isset(self::ROUTE_PATTERNS[$permission])) {
                foreach (self::ROUTE_PATTERNS[$permission] as $pattern) {
                    if ($this->matchesPattern($route, $pattern)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * Check if route matches pattern (supports wildcards)
     */
    private function matchesPattern(string $route, string $pattern): bool
    {
        // Convert pattern to regex
        $regex = str_replace(['*', '/'], ['.*', '\/'], $pattern);
        $regex = '/^' . $regex . '$/';
        
        return preg_match($regex, $route) === 1;
    }
}
