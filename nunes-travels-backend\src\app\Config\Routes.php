<?php
 
namespace Config;
 
// Create a new instance of our RouteCollection class.
$routes = Services::routes();
 
// Load the system's routing file first, so that the app and ENVIRONMENT
// can override as needed.
if (file_exists(SYSTEMPATH . 'Config/Routes.php'))
{
    require SYSTEMPATH . 'Config/Routes.php';
}
 
/**
 * --------------------------------------------------------------------
 * Router Setup
 * --------------------------------------------------------------------
 */
$routes->setDefaultNamespace('App\Controllers');
$routes->setDefaultController('Home');
$routes->setDefaultMethod('index');
$routes->setTranslateURIDashes(false);
$routes->set404Override();
$routes->setAutoRoute(true);
 
 
/*
 * --------------------------------------------------------------------
 * Route Definitions
 * --------------------------------------------------------------------
 */
 
$routes->get('/', 'Home::index');

// We get a performance increase by specifying the default
// route since we don't have to scan directories.
$routes->group('', ['filter' => 'cors'], function ($routes) {
    $routes->options('api/(:any)', function () {});

    $routes->post('/api/login', 'Logon::login');
    $routes->get('/api/logout', 'Logon::logout');

    // Common routes accessible to all authenticated users
    $routes->group('api',['filter' => 'AuthCheck'], function ($routes) {
        $routes->get('user/profile', 'Logon::profile');
        $routes->put('user/profile', 'Logon::updateProfile');
    });

    // Admin-only routes
    $routes->group('api',['filter' => ['AuthCheck', 'role']], function ($routes) {
        $routes->post('set-db-year', 'Logon::setDBYear');
        $routes->resource('users', ['controller' => 'Logon', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
    });

    // Hires management routes (accessible to both Admin and Editor)
    $routes->group('api',['filter' => ['AuthCheck', 'role']], function ($routes) {
        $routes->get('drivers/search', 'Driver::search');
        $routes->post('drivers/record-absence', 'Driver::recordAbsence');
        $routes->get('drivers/absences/(:num)', 'Driver::getAbsences/$1');
        $routes->get('drivers/absences', 'Driver::getAbsences');
        $routes->get('drivers/attendance-overview', 'Driver::getAttendanceOverview');
        $routes->get('drivers/single-attendance/(:num)', 'Driver::getSingleAttendance/$1');

        $routes->post('subagent/rates', 'Rates::ratesForSubagent');
        $routes->post('subagent/tariff', 'Tariff::tariffForSubagent');
        $routes->post('tariff/delete-row', 'Tariff::deleteRow');

        $routes->post('contract/proforma', 'Contract::contractByProforma');
        $routes->post('hirechart/proforma', 'Hirechart::hirechartByProforma');

        $routes->post('cpart/cid', 'Cpart::cpartByCid');

        $routes->post('stats', 'Hirechart::stats');
        $routes->post('hirechart/filtered', 'Hirechart::getHirecharts');

        $routes->post('company/name', 'Company::showByName');
        $routes->post('company2/name', 'Company2::showByName');

        $routes->get('rates/all', 'Rates::allRates');

        $routes->post('general-tariff/vehicle-type', 'Gtariff::gtariffByVehicleType');

        // Hires management resource routes
        $routes->resource('contact', ['controller' => 'Contact', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('cpart', ['controller' => 'Cpart', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('contract', ['controller' => 'Contract', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('hirechart', ['controller' => 'Hirechart', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('company', ['controller' => 'Company', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('company2', ['controller' => 'Company2', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('rates', ['controller' => 'Rates', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('general-tariff', ['controller' => 'Gtariff', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('tariff', ['controller' => 'Tariff', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('vehicle-no', ['controller' => 'VehNo', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('driver', ['controller' => 'Driver', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('vehicle-type', ['controller' => 'Vtype', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('transfer', ['controller' => 'Transfer', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('owner-code', ['controller' => 'Owncode', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('hotel', ['controller' => 'Hotel', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('sector', ['controller' => 'Sector', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('extra', ['controller' => 'Extra', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('gst', ['controller' => 'Gst', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
    });

    // Bills management routes (Admin only)
    $routes->group('api',['filter' => ['AuthCheck', 'role']], function ($routes) {

        $routes->put('bill-reg/bill-no', 'Billreg::updateByBillnum');

        $routes->post('bill-reg/last-created', 'Billreg::getLastCreated');
        $routes->post('bill-reg/remove', 'Billreg::remove');

        $routes->post('bill-reg/filtered', 'Billreg::getBillRegs');
        $routes->post('bill-reg/search', 'Billreg::getBillRegsSearch');

        $routes->post('bill/hid', 'Bill::getBillByHid');
        $routes->put('bill/hid', 'Bill::updateBillByHid');

        $routes->post('dbill-hires/billNo', 'Dbillhires::getByBillNo');

        $routes->post('ptoll/hid/delete', 'Ptoll::deletePtollByHid');
        $routes->post('ptoll/many', 'Ptoll::addMany');

        // Bills management resource routes
        $routes->resource('bill-reg', ['controller' => 'Billreg', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('bill', ['controller' => 'Bill', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('dbill-hires', ['controller' => 'Dbillhires', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('ptoll', ['controller' => 'Ptoll', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('bill-date', ['controller' => 'Billdate', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('proformareg', ['controller' => 'Proformareg', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('proforma', ['controller' => 'Proforma', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
    });

    // Control panel routes (Admin only)
    $routes->group('api',['filter' => ['AuthCheck', 'role']], function ($routes) {

        // Control panel resource routes
        $routes->resource('staff', ['controller' => 'Staff', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('general-rates', ['controller' => 'Grates', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('sub-agent', ['controller' => 'Subagent', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('additional', ['controller' => 'Additional', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
        $routes->resource('projects', ['controller' => 'Project', 'only' => ['index', 'show', 'create', 'update', 'delete']]);
    });
});
/*
 * --------------------------------------------------------------------
 * Additional Routing
 * --------------------------------------------------------------------
 *
 * There will often be times that you need additional routing and you
 * need it to be able to override any defaults in this file. Environment
 * based routes is one such time. require() additional route files here
 * to make that happen.
 *
 * You will have access to the $routes object within that file without
 * needing to reload it.
 */
if (file_exists(APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php'))
{
    require APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php';
}

