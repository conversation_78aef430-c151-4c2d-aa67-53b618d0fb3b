<?php
namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddEditorUser extends Migration
{
    public function up()
    {
        // Check if editor user already exists to avoid duplicates
        $builder = $this->db->table('logon');
        $existingEditor = $builder->where('useremail', 'editor')->get()->getRow();
        
        if (!$existingEditor) {
            // Insert Editor user with userlevel = 3
            $data = [
                'useremail' => 'editor',
                'password' => md5('editor123'), // Default password, should be changed
                'firstname' => 'Editor',
                'lastname' => 'User',
                'userlevel' => 3
            ];
            
            $builder->insert($data);
        }
    }

    public function down()
    {
        // Remove the editor user
        $builder = $this->db->table('logon');
        $builder->where('useremail', 'editor')->delete();
    }
}
